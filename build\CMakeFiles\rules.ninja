# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: ble_slave
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__ble_slave.2eelf_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__ble_slave.2eelf_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-g++.exe $FLAGS $LINK_FLAGS @$RSP_FILE -o $TARGET_FILE && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_riscv_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_riscv_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_riscv_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_gpio_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_gpio_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_pm_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_pm_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_mbedtls_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_mbedtls_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_mbedtls_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__everest_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__everest_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__p256m_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__p256m_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedcrypto_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedcrypto_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedx509_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedx509_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedtls_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedtls_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_app_format_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_app_format_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_bootloader_format_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_bootloader_format_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_app_update_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_app_update_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_partition_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_partition_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_efuse_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_efuse_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_bootloader_support_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_bootloader_support_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_mm_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_mm_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_spi_flash_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_spi_flash_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_system_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_system_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_common_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_common_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_rom_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_rom_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_hal_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_hal_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_log_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_log_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_heap_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_heap_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_soc_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_soc_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_hw_support_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_hw_support_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_freertos_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_freertos_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_freertos_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_newlib_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_newlib_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_pthread_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_pthread_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_cxx_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_cxx_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_timer_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_timer_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_gptimer_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_gptimer_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_ringbuf_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_ringbuf_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_uart_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_uart_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_app_trace_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_app_trace_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_event_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_event_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_nvs_flash_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_nvs_flash_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_spi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_spi_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_i2s_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_i2s_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_sdmmc_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_sdmmc_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_sdspi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_sdspi_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_rmt_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_rmt_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_tsens_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_tsens_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_sdm_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_sdm_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_i2c_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_i2c_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_ledc_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_ledc_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_usb_serial_jtag_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_usb_serial_jtag_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_driver_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_driver_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_phy_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_phy_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_vfs_console_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_vfs_console_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_vfs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_vfs_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_lwip_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_lwip_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_netif_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_netif_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_wpa_supplicant_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wpa_supplicant_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_coex_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_coex_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_wifi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_wifi_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_gdbstub_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_gdbstub_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_bt_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_bt_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_unity_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_unity_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_cmock_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_cmock_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_console_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_console_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_http_parser_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_http_parser_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp-tls_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp-tls_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_adc_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_adc_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_driver_cam_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_driver_cam_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_eth_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_eth_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_hid_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_hid_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_tcp_transport_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_tcp_transport_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_http_client_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_http_client_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_http_server_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_http_server_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_https_ota_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_https_ota_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_https_server_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_https_server_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_lcd_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_lcd_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_protobuf-c_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_protobuf-c_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_protocomm_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_protocomm_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_local_ctrl_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_local_ctrl_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_espcoredump_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_espcoredump_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_wear_levelling_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wear_levelling_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_fatfs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_fatfs_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_json_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_json_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_mqtt_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_mqtt_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_nvs_sec_provider_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_nvs_sec_provider_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_spiffs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_spiffs_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_wifi_provisioning_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wifi_provisioning_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_main_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_main_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_main_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -E rm -f $TARGET_FILE && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && E:\esp_tool\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SE:\esp32_space\ble_mill_monitor_slave -BE:\esp32_space\ble_mill_monitor_slave\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = E:\esp_tool\tools\cmake\3.30.2\bin\cmake.exe -DCONFIG=$CONFIG -P CMakeFiles\clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = E:\esp_tool\tools\ninja\1.12.1\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = E:\esp_tool\tools\ninja\1.12.1\ninja.exe -t targets
  description = All primary targets available:

