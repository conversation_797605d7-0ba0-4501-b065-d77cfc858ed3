# ninja log v6
15562	15807	7753095145595201	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
9	2035	7753094990065182	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	cb78176f3653dddc
83	2166	7753094990805179	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	31643bd8ceda0e35
162	2257	7753094991595173	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	30f51d844e329496
240	2272	7753094992375199	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b8ba88626490e6d2
348	2460	7753094993455183	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	25e11b047edc83c0
6447	10017	7753095054445189	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	b80ca273a9dbb954
489	2607	7753094994865184	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0886aec47ac34ed
6330	9657	7753095053275194	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	1b857d8603efa092
8961	14712	7753095079585175	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	885af7f345ad7210
5182	7631	7753095041795193	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a0cddf8baf8a7725
622	2792	7753094996195182	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	937bd73ff42bf221
1911	4246	7753095009085182	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	497155e73f89de4d
827	3057	7753094998245202	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	649cdad656f8b4f8
13365	15135	7753095123625187	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	e9fa507b2c68099a
726	2936	7753094997235201	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	73d2ea14d681591b
923	3199	7753094999205185	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	58f01bb682f2e3d7
1049	3316	7753095000465175	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e1b94d12daf9ffd8
3939	6447	7753095029365193	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	647702da2488015a
4868	7288	7753095038655189	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	b967448cdfaa7383
1163	3439	7753095001605181	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cdfb41a536fb8bbd
1290	3537	7753095002885192	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	5584ae294dc8bc2c
1411	3682	7753095004085172	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	210943843935fd9b
1549	3810	7753095005465215	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	673ea9e469617ab
8159	13975	7753095071565190	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f7cd516dfa5aa97
1644	3939	7753095006415206	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	483192b6cbcd8ea8
1767	4075	7753095007645216	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cfdd251618f6a714
2036	4401	7753095010335181	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	14c31d09cb487174
2166	4582	7753095011635179	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	6aa3570ab1cd2ae2
3057	5533	7753095020545168	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	63f8478ca2cd3234
2258	4716	7753095012555204	esp-idf/log/liblog.a	c91a251e1ec6f52c
3316	5765	7753095023135176	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4824227fe51f023e
2272	4726	7753095012695208	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ca32f6349de14497
12576	15134	7753095115735197	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	36a0b2d6be3ef1b
4582	6997	7753095035795170	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	70b39228ff3c3953
2460	4868	7753095014575193	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	c0df74edbaa8d4e2
5398	7758	7753095043955206	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	ffdd27642721c0bf
2608	5035	7753095016065191	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	4bd0cfcf61b4f9eb
2792	5181	7753095017895186	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8a2798b39b38967
6874	11048	7753095058715177	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	7337709c27fee1ca
2936	5398	7753095019335257	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	83e187d5649e4953
4726	7123	7753095037235196	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6790c1df7306deb2
3200	5651	7753095021975203	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	fda948f7d94318fe
7631	13006	7753095066295189	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	8f538409bc2cd6cc
3682	6198	7753095026795208	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4b64013a1931ce9e
3439	5920	7753095024365184	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	142b08fee5111a88
3537	6052	7753095025345194	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	858d4baa72e0a17b
3810	6330	7753095028075182	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	bf5475bb65ab941c
7123	11754	7753095061205187	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	35c22edb96f4c16a
4076	6638	7753095030735206	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	83781b0edbf4dd06
5035	7485	7753095040325175	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	606a76877fef8d81
7957	13704	7753095069545202	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	a1902cbe249b4406
7114	11743	7753095061115180	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
4246	6747	7753095032435186	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	dcae97481a03c066
7485	12576	7753095064825182	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	28f496dae6971f53
4401	6874	7753095033985183	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	fc218c3d43777ba7
4716	7114	7753095037135191	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
5533	7957	7753095045305206	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b77bc6b182498ad1
5651	8159	7753095046485189	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4ebae100feb37f9a
6638	10351	7753095056355219	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	6e27940ab0c3e7c
5765	8392	7753095047625196	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	7a3f3769d1ee15cd
6052	8961	7753095050495185	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	abca8987946c4
12	296	7753108731392046	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
10715	15123	7753095097125182	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	29b8fde4d883d840
5921	8669	7753095049185177	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	233500e586f9ee34
6198	9296	7753095051955224	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	889baa59c793157
6747	10715	7753095057445851	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	749b6846e469439c
6997	11366	7753095059945183	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	25b380721e1b29b6
7288	12182	7753095062855177	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	be38ffb92a960c1f
7758	13365	7753095067555188	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61d58f48e37c570c
8392	14295	7753095073895195	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	2846994fd4688db
8670	14493	7753095076675178	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	24015bfc507f4dc0
9296	14917	7753095082935230	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	445508437b2f4d37
9658	14926	7753095086555191	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	6d24e56d1ded1fb9
10017	15122	7753095090145211	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	97f79caf25082d69
10351	15122	7753095093485196	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	1abbd206e65300d7
11048	15123	7753095100455188	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	754ba61bf0dd9e65
11366	15124	7753095103635180	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	d3b413fd7717610e
11743	15124	7753095107406433	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
11754	15133	7753095107515195	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	2fbd10801eda5f93
12183	15133	7753095111805222	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	9da01f23b4cebb82
17005	17246	7753095160035186	esp-idf/soc/libsoc.a	2ed56a05eac453da
13006	15134	7753095120035210	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	58accd046d586db1
13704	15135	7753095127015196	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	d880b4553289daf1
13975	15135	7753095129725178	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	1af531e95af75350
14295	15136	7753095132925185	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	88ce9b6940f96e5c
14917	15199	7753095141915172	project_elf_src_esp32c3.c	6c7d774e998bc721
14917	15199	7753095141915172	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	6c7d774e998bc721
14493	15341	7753095134905193	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	7ab51ff84ae785a9
14713	15383	7753095137105177	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b9f0ed134b19d3fd
14926	15510	7753095139235195	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	74d83039a25dbef3
15124	15562	7753095141215193	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
15199	15587	7753095141965187	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	fadd1928e73520b8
15807	16077	7753095148045183	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
16077	16280	7753095150745214	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
16280	16518	7753095152775218	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
16518	16775	7753095155155201	esp-idf/hal/libhal.a	38abfeb38a2a248d
16776	17005	7753095157735179	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
17247	17462	7753095162445186	esp-idf/main/libmain.a	5bc613d7954c6145
17463	17753	7753095164605180	bootloader.elf	fced577b480f0e0d
17753	18277	7753095172705194	.bin_timestamp	d7e60687dc9cd5c8
17753	18277	7753095172705194	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	d7e60687dc9cd5c8
12	296	7753108731392046	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
5	33923	7753136698847473	build.ninja	ab2e4b4657482c83
41	952	7753136710674814	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
41	952	7753136710674814	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
10	238	7753138342328905	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
10	238	7753138342328905	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
9	183	7753139223859018	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
9	183	7753139223859018	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	5483a65cd6962678
