/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include "ble_gatt.h"
#include "i2c_ms583730b.h"
#include "adc_ntc.h"
#include "dev_id.h"

#define GATTS_TABLE_TAG "BLE-SLAVE"

// 全局功能表
static uint16_t slave_handle_table[HRS_IDX_NB];
//
// 应用实例表（全局），用于管理所有注册到 BLE 协议栈的 GATT 应用实例
// 每个实例结构体包含：
// 回调函数 gatts_cb：处理属于该实例的所有事件
// 接口句柄 gatts_if：协议栈分配的 GATT 接口 ID，用于事件分发和调用相关 API
static struct gatts_profile_inst gatt_app_profile_table[PROFILE_NUM] = {
    [SLAVE_PROFILE_IDX] = {
        .gatts_cb = gatts_slave_profile_event_handler,
        .gatts_if = ESP_GATT_IF_NONE, // 初始为空，待注册成功后由协议栈赋值
    },
};

/* Service */
static const uint16_t GATTS_SERVICE_UUID_SENSOR = 0x00FF;
static const uint16_t GATTS_CHAR_UUID_SLAVE_RESPONSE = 0xFF01; // 响应特征UUID
static const uint16_t GATTS_CHAR_UUID_SLAVE_CONFIG = 0xFF02;   // 响应特征UUID
//
static const uint16_t primary_service_uuid = ESP_GATT_UUID_PRI_SERVICE;
static const uint16_t character_declaration_uuid = ESP_GATT_UUID_CHAR_DECLARE;
static const uint8_t char_prop_read_write = ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_READ;
//
static ms5837_t sensor; // 初始化 MS5837 传感器
//
static struct gatts_slave_response slave_response;
//
static struct gatts_slave_config slave_config = {
    .sample_rate = 1 // 默认采样频率 1 Hz
};

// 使用ADC采样电路测温
esp_err_t read_temperature_sensor(float *temp)
{
    esp_err_t ret = ESP_OK;
    *temp = 0;
    return ret;
    /*
    esp_err_t ret;
    float temp = 0;
    ret = adc_ntc_read_chan0(&temp);
    if (ret == ESP_OK)
    {
        ESP_LOGW(GATTS_TABLE_TAG, "读取温度成功: %.1f°C", temp);
    }
    else
    {
        ESP_LOGW(GATTS_TABLE_TAG, "读取温度失败: %s", esp_err_to_name(ret));
    }
    return temp;
    */
}

// 使用MS5837取压力(I2C通讯)
esp_err_t read_pressure_sensor(float *press)
{
    esp_err_t ret = ESP_OK;
    *press = 0;
    return ret;
    /*
    esp_err_t ret;
    float temp = 0, press = 0;
    ret = ms5837_get_data(&sensor, &temp, &press);
    if (ret == ESP_OK)
    {
        ESP_LOGW(GATTS_TABLE_TAG, "MS5837 读取成功 压力: %.1f mbar, 温度: %.1f°C", press, temp);
    }
    else
    {
        ESP_LOGE(GATTS_TABLE_TAG, "MS5837 读取失败 错误: %s", esp_err_to_name(ret));
    }
    return press;
    */
}

// 该数组的元素顺序与枚举类型定义的顺序一致，请严格按顺序赋值
static const esp_gatts_attr_db_t gatt_db[HRS_IDX_NB] =
    {
        // 服务声明 - Service Declaration
        [IDX_SVC] =
            // 由协议栈自动回复,通常是在初始化时就固定的值,句柄由协议栈自动处理,无须手动处理
        {{ESP_GATT_AUTO_RSP},
         // BLE GATT中有主服务(Primary Service)和次服务(Secondary Service)两种类型:
         // - 主服务(0x2800): 独立的功能单元,可被客户端直接发现和使用,会在广播中暴露
         // - 次服务(0x2801): 辅助性服务,通常被主服务引用,不能独立存在,不在广播中主动暴露
         // 大多数应用使用主服务即可,复杂项目才需要设计次服务的层次结构
         //
         // 参数说明:
         // - primary_service_uuid: 服务类型标识符(0x2800,蓝牙联盟规定的主服务UUID)
         // - GATTS_SERVICE_UUID_SENSOR: 具体服务的UUID(0x00FF,开发者自定义的服务标识)
         // - 根据BLE GATT规范，服务声明属性必须是只读的，这是蓝牙联盟的强制要求
         {ESP_UUID_LEN_16, (uint8_t *)&primary_service_uuid, ESP_GATT_PERM_READ, sizeof(uint16_t), sizeof(GATTS_SERVICE_UUID_SENSOR), (uint8_t *)&GATTS_SERVICE_UUID_SENSOR}},

        // 采集数据 -> 特征声明
        [IDX_CHAR_SLAVE_RESPONSE] =
            // 由协议栈自动回复，句柄由协议栈自动处理
        {{ESP_GATT_AUTO_RSP},
         // 特征声明的作用：
         // - UUID固定为0x2803(蓝牙联盟规定的特征声明标识符)，告诉客户端"这里是一个特征声明"
         // - 包含特征属性信息(可读/可写/可通知等)，客户端通过读取此声明了解特征的能力
         // - 按GATT规范，特征声明后(按枚举类型定义的格式顺序排布)紧跟的下一个成员就是对应的特征值，无需额外关联,
         // - 大概起一个标志符的作用,像是告诉主机, 我是一个特征声明,下面紧挨着的就是特征值(如果顺序不错的话), 你不用去别的地方找了, 就是下面这个
         // 参数说明：
         // - char_prop_read_write_notify: 特征属性(支持读/写/通知)
         // 注意对比：服务声明的最后一个参数是服务UUID(自定义)，而特征声明的最后参数是特征属性
         // 原因：UUID决定了该ATT属性的"身份类型"，不同类型的属性需要存储不同格式的"值"
         // - 服务声明(0x2800) → 值为该服务的UUID
         // - 特征声明(0x2803) → 值为该特征的属性标志(读/写/通知等能力)
         // 原则上,这里做为第一道防线, 我允许主机对下面的特征值,读写通知, 但是具体能不能操作,也看他自己对权限的定义
         // 但是,如果我这里就拒绝(例: 写权限),那么即使他自己定义写权限也是白搭,因为这里已经拦截了
         {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ, CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_read_write}},

        // 采集数据 -> 特征值
        [IDX_CHAR_VAL_SLAVE_RESPONSE] =
            // ESP_GATT_AUTO_RSP : 不管主机问不问, 我都在定时器中自动采集, 放在缓冲区中, 当主机问的时候, 协议栈自动将缓冲区的值返回, 不用我管
            // ESP_GATT_RSP_BY_APP : 只当主机问我的时候, 我才采集一次, 他不问我就不采集, 主打一个节能降耗,(需要在事件回调中手动处理)
        {{ESP_GATT_RSP_BY_APP},
         // 因为上面的特征声明已经告诉你了(主机), 他是特征声明而下面紧接的成员(也就是我)就是特征值了
         // 而我,要通过UUID标识出来我是什么值(温度?重量?)而这些UUID都是主从事先约定好的(是开发者自定义的不是蓝牙联盟), 通信时通过此UUID就知道我代表什么数据了
         // GATTS_DEMO_CHAR_VAL_LEN_MAX : 这个特征值最多能存储多少数据 最大长度：500字节
         {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_SLAVE_RESPONSE, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE, GATTS_DEMO_CHAR_VAL_LEN_MAX, sizeof(slave_response), (uint8_t *)&slave_response}},

        // 从机参数配置 -> 可由主机修改
        [IDX_CHAR_SLAVE_CONFIG] =
            // 由协议栈自动回复，句柄由协议栈自动处理
        {{ESP_GATT_AUTO_RSP},
         {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ, CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_read_write}},

        // 更新从机参数配置
        [IDX_CHAR_VAL_SALVE_CONFIG] =
            // 只有 ESP_GATT_RSP_BY_APP 才会触发写事件
        {{ESP_GATT_RSP_BY_APP},
         {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_SLAVE_CONFIG, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE, GATTS_DEMO_CHAR_VAL_LEN_MAX, sizeof(slave_config), (uint8_t *)&slave_config}},

};

// gatts_event_handler 通过判断gatts_if将事件及参数分派给对应的应用实例的事件处理函数(也就是这个函数)
void gatts_slave_profile_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    switch (event)
    {

    // 使能esp_ble_gatts_app_register()注册应用后,协议栈会触发该事件
    case ESP_GATTS_REG_EVT:
    {
        // 此时BLE协议栈已经完全初始化
        // GAP层功能已经可以使用
        // 设置设备名称的API才能正常工作
        // 太早设置 (比如在app_main中)：协议栈还没准备好，API调用可能失败
        // 太晚设置 (比如在连接事件中)：广播已经开始，设备名称已经对外暴露,再设置就晚了

        // 获取已缓存的设备固定信息
        esp_err_t err;
        err = get_device_id(&slave_response.device_id);
        if (err == ESP_OK)
        {
            ESP_LOGI(GATTS_TABLE_TAG, "获取设备ID成功: %d", slave_response.device_id);
        }
        else
        {
            ESP_LOGE(GATTS_TABLE_TAG, "获取设备ID失败: %s", esp_err_to_name(err));
        }

        err = get_device_mac(&slave_response.device_mac[0]);
        if (err == ESP_OK)
        {
            ESP_LOGI(GATTS_TABLE_TAG, "获取设备MAC成功: %02X:%02X:%02X:%02X:%02X:%02X",
                     slave_response.device_mac[0], slave_response.device_mac[1], slave_response.device_mac[2],
                     slave_response.device_mac[3], slave_response.device_mac[4], slave_response.device_mac[5]);
        }
        else
        {
            ESP_LOGE(GATTS_TABLE_TAG, "获取设备MAC失败: %s", esp_err_to_name(err));
        }

        // 设置广播中的设备名称
        esp_err_t set_dev_name_ret = ble_gap_set_device_name(SAMPLE_DEVICE_NAME);
        if (set_dev_name_ret != ESP_OK)
        {
            ESP_LOGE(GATTS_TABLE_TAG, "set device name failed, error code = %x", set_dev_name_ret);
        }

        // 配置广播数据
        esp_err_t adv_config_ret = ble_gap_config_adv_data();
        if (adv_config_ret != ESP_OK)
        {
            ESP_LOGE(GATTS_TABLE_TAG, "config adv data failed, error code = %x", adv_config_ret);
        }

        // 将定义好属性表加载到协议栈,完成后会触发ESP_GATTS_CREAT_ATTR_TAB_EVT事件
        esp_err_t create_attr_ret = esp_ble_gatts_create_attr_tab(gatt_db, gatts_if, HRS_IDX_NB, SVC_INST_ID);
        if (create_attr_ret)
        {
            ESP_LOGE(GATTS_TABLE_TAG, "create attr table failed, error code = %x", create_attr_ret);
        }
    }
    break;
    case ESP_GATTS_READ_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "ESP_GATTS_READ_EVT, handle = %d ", param->read.handle);

        esp_gatt_rsp_t rsp;
        memset(&rsp, 0, sizeof(esp_gatt_rsp_t));

        // 设置响应的句柄，告诉协议栈这是对哪个属性读取请求的回复
        rsp.attr_value.handle = param->read.handle;

        // 从机响应
        if (slave_handle_table[IDX_CHAR_VAL_SLAVE_RESPONSE] == param->read.handle)
        {
            esp_err_t err;
            // 实时采集数据

            err = read_temperature_sensor(&slave_response.temperature);
            err = read_pressure_sensor(&slave_response.pressure);

            slave_response.temperature = 28;

            if (err == ESP_OK)
            {
                // esp_gatt_rsp_t rsp 是协议栈规定的应答格式
                memcpy(rsp.attr_value.value, &slave_response, sizeof(slave_response));
                rsp.attr_value.len = sizeof(slave_response);

                // 发送响应给主机
                esp_ble_gatts_send_response(gatts_if, param->read.conn_id, param->read.trans_id,
                                            ESP_GATT_OK, &rsp);

                ESP_LOGW(GATTS_TABLE_TAG, "设备ID: %d MAC: %02X:%02X:%02X:%02X:%02X:%02X  温度: %.1f°C  压力: %.1f kPa",
                         slave_response.device_id,
                         slave_response.device_mac[0], slave_response.device_mac[1], slave_response.device_mac[2],
                         slave_response.device_mac[3], slave_response.device_mac[4], slave_response.device_mac[5],
                         slave_response.temperature, slave_response.pressure);
            }
        }

        break;
    case ESP_GATTS_WRITE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "ESP_GATTS_WRITE_EVT, handle = %d", param->write.handle);

        if (slave_handle_table[IDX_CHAR_VAL_SALVE_CONFIG] == param->write.handle)
        {
            // 主机写入配置参数
            if (param->write.len == sizeof(struct gatts_slave_config))
            {
                // 先检查数据合法性
                struct gatts_slave_config *new_config = (struct gatts_slave_config *)param->write.value;

                if (new_config->sample_rate > 0 && new_config->sample_rate <= 20)
                {
                    // 更新实际的采样频率设置
                    memcpy(&slave_config, param->write.value, param->write.len);

                    // 应用新的配置参数
                    ESP_LOGI(GATTS_TABLE_TAG, "配置更新: 采样频率 = %d Hz", slave_config.sample_rate);
                }
                else
                {
                    ESP_LOGW(GATTS_TABLE_TAG, "采样频率非法: %d Hz", slave_config.sample_rate);
                }
            }
            else
            {
                ESP_LOGW(GATTS_TABLE_TAG, "配置数据长度错误: 期望 %d, 实际 %d",
                         sizeof(struct gatts_slave_config), param->write.len);
            }

            // 发送写响应
            if (param->write.need_rsp)
            {
                esp_ble_gatts_send_response(gatts_if, param->write.conn_id,
                                            param->write.trans_id, ESP_GATT_OK, NULL);
            }
        }
        break;
    case ESP_GATTS_START_EVT:
        if (param->start.status != ESP_GATT_OK)
        {
            ESP_LOGE(GATTS_TABLE_TAG, "start service failed, error status = %x", param->start.status);
        }
        else
        {
            ESP_LOGI(GATTS_TABLE_TAG, "start service successfully, service_handle %d", param->start.service_handle);
            // 服务已启动，现在可以被主机发现和访问了
        }
        break;

    // GAP层建立连接后,无须手动处理,协议栈会自动触发该事件
    case ESP_GATTS_CONNECT_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "ESP_GATTS_CONNECT_EVT, conn_id = %d", param->connect.conn_id);

        // 打印远程设备的蓝牙地址(6字节MAC地址)
        esp_log_buffer_hex(GATTS_TABLE_TAG, param->connect.remote_bda, 6);

        // 连接参数优化：主机建立连接时使用的默认参数可能不是最优的
        // 从机可以请求更新连接参数来优化性能和功耗
        esp_ble_conn_update_params_t conn_params = {0};
        memcpy(conn_params.bda, param->connect.remote_bda, sizeof(esp_bd_addr_t));

        // 设置优化后的连接参数
        // 对于iOS系统，请参阅苹果官方文件中关于BLE连接参数的限制
        // 注意：min_int 必须 <= max_int
        // 从机端优化参数
        conn_params.latency = 0;    // 改为0，确保立即响应
        conn_params.min_int = 0x25; // 0x18 * 1.25ms = 30ms
        conn_params.max_int = 0x30; // 0x30 * 1.25ms = 60ms
        conn_params.timeout = 600;  // 保持6秒超时

        // 向主机发送连接参数更新请求，主机可以接受或拒绝
        esp_ble_gap_update_conn_params(&conn_params);
        break;

    // 连接断开,重新恢复广播
    case ESP_GATTS_DISCONNECT_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "ESP_GATTS_DISCONNECT_EVT, reason = 0x%x", param->disconnect.reason);
        ble_gap_start_advertising();
        break;

    // esp_ble_gatts_create_attr_tab()将定义好属性表加载到协议栈,完成后会触发该事件
    case ESP_GATTS_CREAT_ATTR_TAB_EVT:
    {
        // 检查属性表创建状态
        if (param->add_attr_tab.status != ESP_GATT_OK)
        {
            // 创建失败，打印错误码
            ESP_LOGE(GATTS_TABLE_TAG, "create attribute table failed, error code=0x%x", param->add_attr_tab.status);
        }
        else if (param->add_attr_tab.num_handle != HRS_IDX_NB)
        {
            // 创建的句柄数量与预期不符，可能是定义有问题
            ESP_LOGE(GATTS_TABLE_TAG, "create attribute table abnormally, num_handle (%d) \
                        doesn't equal to HRS_IDX_NB(%d)",
                     param->add_attr_tab.num_handle, HRS_IDX_NB);
        }
        else
        {
            // 属性表创建成功
            ESP_LOGI(GATTS_TABLE_TAG, "create attribute table successfully, the number handle = %d", param->add_attr_tab.num_handle);

            // 保存协议栈分配的句柄到全局数组，后续通过句柄来识别具体的属性
            // esp_ble_gatts_create_attr_tab()从机把属性表加载到协议栈后, 协议栈分配所有句柄,
            // 主机在通讯中会遍历所有服务, 服务下的特征声明, 特征值,  特征配置
            // 从机提供服务 → 协议栈分配句柄 → 主机自动发现 → 建立完整映射 - 后续通信完全基于句柄！
            memcpy(slave_handle_table, param->add_attr_tab.handles, sizeof(slave_handle_table));

            // 启动服务，使其对外可见和可访问
            // 使用服务的句柄(IDX_SVC对应的句柄)来启动整个服务
            esp_ble_gatts_start_service(slave_handle_table[IDX_SVC]);
        }
        break;
    }
    default:
        break;
    }
}
// event : 发生事件
// gatts_if : 协议栈分配的gatt_if, 用于识别是哪个APP发出的事件
// param : 事件参数, 不同事件携带不同参数, 具体参考 esp_ble_gatts_cb_param_t 结构体定义
// 所有的gatt事件先经过这里, 再通过gatts_if判断将该事件及参数分派给对应的应用实例
void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    // 应用注册成功后,协议栈会为每个应用分配一个gatts_if
    if (event == ESP_GATTS_REG_EVT)
    {
        if (param->reg.status == ESP_GATT_OK)
        {
            // heart_rate_profile_tabble[] 是一个 Profile(应用实例) 表，保存了每个服务的回调函数、gatts_if等信息
            // 开发者需要自己把gatt_if存储在 profile 表中，
            // 后续调用 BLE 协议栈中的大部分 GATT Server API，都需要用到对应的 gatts_if。
            // 这是 BLE 协议栈用来识别"你想操作哪个 GATT 应用"的唯一方式。
            gatt_app_profile_table[SLAVE_PROFILE_IDX].gatts_if = gatts_if;
        }
        else
        {
            ESP_LOGE(GATTS_TABLE_TAG, "reg app failed, app_id %04x, status %d",
                     param->reg.app_id,
                     param->reg.status);
            return;
        }
    }
    do
    {
        int idx;
        for (idx = 0; idx < PROFILE_NUM; idx++)
        {
            // 遍历gatt_app_profile_table中的所有服务,
            // 通过gatts_if来判断该事件是由哪个服务发出的,由该服务的回调函数来处理。
            // 如果gatts_if == ESP_GATT_IF_NONE, 说明事件不特定于某个应用，需要所有应用都处理。
            if (gatts_if == ESP_GATT_IF_NONE || gatts_if == gatt_app_profile_table[idx].gatts_if)
            {
                if (gatt_app_profile_table[idx].gatts_cb)
                {
                    gatt_app_profile_table[idx].gatts_cb(event, gatts_if, param);
                }
            }
        }
    } while (0);
}

// GATT 层初始化
esp_err_t ble_gatt_init(void)
{
    esp_err_t ret;

    // 注册 GATT 事件回调函数，BLE 核心事件（如连接、读写请求）必须通过这个回调接收处理
    ret = esp_ble_gatts_register_callback(gatts_event_handler);
    if (ret != ESP_OK)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "gatts register error, error code = %x", ret);
        return ret;
    }

    // 协议栈支持管理多个应用 → 应用管理多个服务 → 服务管理多个特征 → 应用的回调函数负责处理所有对应事件。
    // 每个应用注册自己的回调函数（gatts_cb），统一接收该应用所有服务、特征的事件
    // 每个应用由开发者指定一个 APP_ID, 通过调用该函数把应用注册到协议栈中
    // 注册成功后,事件回调函数 gatts_event_handler() 会捕获 ESP_GATTS_REG_EVT 事件
    // 协议栈会为每个应用分配一个gatt_if，开发者需要自己保存, 后续调用 BLE 协议栈中的大部分 GATT Server API，都需要用该 gatts_if
    ret = esp_ble_gatts_app_register(ESP_APP_ID);
    if (ret != ESP_OK)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "gatts app register error, error code = %x", ret);
        return ret;
    }

    // 设置本地最大 MTU（最大传输单元），提升 BLE 数据吞吐能力， 注意 4.2和5.0的最大MTU是不同的
    // 默认是 23，可调大，但最终大小由对端协商结果决定
    ret = esp_ble_gatt_set_local_mtu(200);
    if (ret != ESP_OK)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "set local  MTU failed, error code = %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(GATTS_TABLE_TAG, "GATT initialized successfully");

    /*
    // 初始化 MS5837 传感器
    ret = ms5837_init(&sensor);
    if (ret == ESP_OK)
    {
        ESP_LOGI(GATTS_TABLE_TAG, "MS5837 sensor initialized successfully");
    }
    else
    {
        ESP_LOGE(GATTS_TABLE_TAG, "Failed to initialize MS5837 sensor: %s", esp_err_to_name(ret));
    }

    */
    // 初始化ADC
    ret = adc_ntc_init();
    if (ret == ESP_OK)
    {
        ESP_LOGI(GATTS_TABLE_TAG, "ADC initialization completed");
    }
    else
    {
        ESP_LOGE(GATTS_TABLE_TAG, "Failed to initialize ADC");
    }

    return ESP_OK;
}